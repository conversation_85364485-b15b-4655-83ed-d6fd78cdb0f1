import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/repository/social_repository.dart';
import 'package:toii_social/model/comment/comment_model.dart';
import 'package:toii_social/model/comment/nested_comment_model.dart';

part 'reply_comment_state.dart';

class ReplyCommentCubit extends Cubit<ReplyCommentState> {
  final String postId;
  final SocialRepository _socialRepository = GetIt.instance<SocialRepository>();

  ReplyCommentCubit({required this.postId}) : super(const ReplyCommentState());

  /// Initialize with main comments
  void initializeComments(List<CommentItemModel> comments) {
    final nestedComments = NestedCommentModel.fromCommentList(comments);
    emit(state.copyWith(nestedComments: nestedComments));
  }

  /// Load replies for a specific comment
  Future<void> loadReplies(String commentId) async {
    if (state.isLoadingReplies(commentId)) return;

    // Set loading state for this specific comment
    final updatedComments =
        state.nestedComments.map((nestedComment) {
          if (nestedComment.id == commentId) {
            return nestedComment.setLoading(true);
          }
          return nestedComment;
        }).toList();

    emit(state.copyWith(nestedComments: updatedComments));

    try {
      final response = await _socialRepository.getRepliesOfComment(commentId);
      final replies = response.data.comments;

      // Update the specific comment with loaded replies
      final finalComments =
          state.nestedComments.map((nestedComment) {
            if (nestedComment.id == commentId) {
              return nestedComment.setLoadedReplies(replies);
            }
            return nestedComment;
          }).toList();

      emit(
        state.copyWith(
          status: ReplyCommentStatus.success,
          nestedComments: finalComments,
          errorMessage: null,
        ),
      );
    } catch (e) {
      // Remove loading state on error
      final errorComments =
          state.nestedComments.map((nestedComment) {
            if (nestedComment.id == commentId) {
              return nestedComment.setLoading(false);
            }
            return nestedComment;
          }).toList();

      emit(
        state.copyWith(
          status: ReplyCommentStatus.failure,
          nestedComments: errorComments,
          errorMessage: 'Failed to load replies: $e',
        ),
      );
    }
  }

  /// Toggle expand/collapse state for a comment's replies
  void toggleExpanded(String commentId) {
    final updatedComments =
        state.nestedComments.map((nestedComment) {
          if (nestedComment.id == commentId) {
            return nestedComment.toggleExpanded();
          }
          return nestedComment;
        }).toList();

    emit(state.copyWith(nestedComments: updatedComments));
  }

  /// Create a reply to a comment
  Future<void> createReply({
    required String parentCommentId,
    required String content,
  }) async {
    emit(
      state.copyWith(isCreatingReply: true, status: ReplyCommentStatus.loading),
    );

    try {
      final request = CreateCommentRequestModel(
        content: content,
        parentCommentId: parentCommentId,
      );

      final response = await _socialRepository.createCommentsOfPost(
        postId,
        request,
      );
      final newReply = response.data;

      // Add the new reply to the specific parent comment and ensure it's expanded
      final updatedComments =
          state.nestedComments.map((nestedComment) {
            if (nestedComment.id == parentCommentId) {
              // Add reply and ensure the comment is expanded to show the new reply
              return nestedComment
                  .addReply(newReply)
                  .copyWith(isExpanded: true);
            }
            return nestedComment;
          }).toList();

      emit(
        state.copyWith(
          status: ReplyCommentStatus.success,
          nestedComments: updatedComments,
          isCreatingReply: false,
          errorMessage: null,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: ReplyCommentStatus.failure,
          isCreatingReply: false,
          errorMessage: 'Failed to create reply: $e',
        ),
      );
    }
  }

  /// Add a new reply to the state (for optimistic updates)
  void addReplyOptimistically(String parentCommentId, CommentItemModel reply) {
    final updatedComments =
        state.nestedComments.map((nestedComment) {
          if (nestedComment.id == parentCommentId) {
            return nestedComment.addReply(reply);
          }
          return nestedComment;
        }).toList();

    emit(state.copyWith(nestedComments: updatedComments));
  }

  /// Clear all state
  void clear() {
    emit(const ReplyCommentState());
  }
}
