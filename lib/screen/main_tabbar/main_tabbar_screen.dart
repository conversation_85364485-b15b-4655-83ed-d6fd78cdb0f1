import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_social/cubit/post/home/<USER>';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/chat/lib/domain/chat_service.dart';
import 'package:toii_social/screen/home/<USER>';
import 'package:toii_social/screen/main_tabbar/tabbar/tabbar_bottom_widget.dart';
import 'package:toii_social/screen/profile/profile_screen.dart';
import 'package:toii_social/screen/wallet/wallet_screen.dart';

enum _SelectedTab { main, green, add, stats, profile }

class MainTabbarScreen extends StatefulWidget {
  const MainTabbarScreen({super.key});

  @override
  State<MainTabbarScreen> createState() => _MainTabbarScreenState();
}

class _MainTabbarScreenState extends State<MainTabbarScreen> {
  var _selectedTab = _SelectedTab.main;
  final ScrollController _scrollController = ScrollController();
  ScrollDirection _currentScrollDirection =
      ScrollDirection.idle; // Default to hide bottom bar
  late final List<Widget> _tabScreens;
  @override
  void initState() {
    ChatService().init();
    _tabScreens = [
      HomeMainPage(
        scrollController: _scrollController,
        onScrollDirectionChanged: (direction) {
          setState(() {
            // Only show bottom bar when scrolling down (from top to bottom)
            _currentScrollDirection = direction;
          });
        },
      ),
      Container(color: Colors.white), // Placeholder for green tab
      Container(color: Colors.white), // Placeholder for add tab
      WalletScreen(), // Placeholder for stats tab
      ProfileScreen(),
    ];
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();

    super.dispose();
  }

  Future<void> _handleIndexChanged(int index) async {
    if (index == 2) {
      final reusult = await context.push(RouterEnums.createPost.routeName);
      if (reusult != null && reusult is PostModel) {
        GetIt.instance<HomeCubit>().getUserFeed();
      }
      return;
    }
    setState(() {
      _selectedTab = _SelectedTab.values[index];
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => GetIt.instance<ProfileCubit>()..getProfile(),

      child: Scaffold(
        extendBody: true,
        backgroundColor: Colors.transparent,
        body: IndexedStack(
          index: _SelectedTab.values.indexOf(_selectedTab),
          children: _tabScreens,
        ),
        bottomNavigationBar: AnimatedContainer(
          duration: const Duration(milliseconds: 400),
          height: _currentScrollDirection == ScrollDirection.reverse ? 105 : 0,
          child: tabbarHome(
            onTap: _handleIndexChanged,
            currentIndex: _SelectedTab.values.indexOf(_selectedTab),
          ),
        ),
      ),
    );
  }
}
