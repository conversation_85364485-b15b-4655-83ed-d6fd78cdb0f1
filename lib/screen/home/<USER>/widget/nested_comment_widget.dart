import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_social/cubit/post/reply_comment/reply_comment_cubit.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/model/comment/comment_model.dart';
import 'package:toii_social/model/comment/nested_comment_model.dart';
import 'package:toii_social/screen/home/<USER>/widget/comment_widget.dart';
import 'package:toii_social/screen/home/<USER>/widget/reply_comment_bottom_sheet.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class NestedCommentWidget extends StatelessWidget {
  final List<CommentItemModel> comments;
  final String postId;

  const NestedCommentWidget({
    super.key,
    required this.comments,
    required this.postId,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create:
          (context) =>
              ReplyCommentCubit(postId: postId)..initializeComments(comments),
      child: <PERSON><PERSON><PERSON>er<ReplyCommentCubit, ReplyCommentState>(
        builder: (context, state) {
          if (state.nestedComments.isEmpty) {
            return const SizedBox.shrink();
          }

          return Column(
            children:
                state.nestedComments.map((nestedComment) {
                  return _NestedCommentItem(
                    nestedComment: nestedComment,
                    depth: 0,
                  );
                }).toList(),
          );
        },
      ),
    );
  }
}

class _NestedCommentItem extends StatelessWidget {
  final NestedCommentModel nestedComment;
  final int depth;

  const _NestedCommentItem({required this.nestedComment, required this.depth});

  @override
  Widget build(BuildContext context) {
    final comment = nestedComment.comment;
    final hasReplies = nestedComment.hasReplies;
    final isExpanded = nestedComment.isExpanded;
    final isLoadingReplies = nestedComment.isLoadingReplies;

    return BlocBuilder<ReplyCommentCubit, ReplyCommentState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Main comment with indentation
            Container(
              margin: EdgeInsets.only(left: depth * 24.0, bottom: 4),
              child: CommentWidget(
                commentItemModel: comment,
                onReply: () {
                  // Determine which comment to reply to based on depth
                  final targetComment =
                      depth == 0
                          ? comment // For main comments, reply to this comment
                          : _findRootComment(
                            context,
                          ); // For reply comments, reply to root comment

                  if (targetComment != null) {
                    ReplyCommentBottomSheet.show(
                      context: context,
                      parentComment: targetComment,
                      // TODO: Get current user info from user state/cubit
                      currentUserName:
                          "You", // Replace with actual current user name
                      currentUserAvatar:
                          "", // Replace with actual current user avatar
                    );
                  }
                },
                showReplyButton:
                    true, // Always show Reply button for consistency
              ),
            ),

            // Reply actions (View/Hide replies button)
            if (hasReplies)
              Container(
                margin: EdgeInsets.only(left: depth * 24.0 + 52, bottom: 8),
                child: _buildReplyActions(
                  context,
                  hasReplies,
                  isExpanded,
                  isLoadingReplies,
                ),
              ),

            // Note: Reply input is now handled by bottom sheet modal

            // Nested replies (loaded from API)
            if (isExpanded && nestedComment.hasLoadedReplies)
              ...nestedComment.loadedReplies.map(
                (replyNested) => _NestedCommentItem(
                  nestedComment: replyNested,
                  depth: depth + 1,
                ),
              ),
          ],
        );
      },
    );
  }

  /// Find the root comment for this nested comment
  /// For depth 0: returns this comment
  /// For depth > 0: finds the root comment from the cubit state
  CommentItemModel? _findRootComment(BuildContext context) {
    if (depth == 0) {
      return nestedComment.comment;
    }

    // For nested comments, find the root comment by traversing up
    // Since we only support 1-level deep, the root is always at depth 0
    final state = context.read<ReplyCommentCubit>().state;

    // Find the root comment that contains this reply
    for (final rootNestedComment in state.nestedComments) {
      if (rootNestedComment.loadedReplies.any(
        (reply) => reply.id == nestedComment.id,
      )) {
        return rootNestedComment.comment;
      }
    }

    // Fallback: if not found, return the first root comment
    return state.nestedComments.isNotEmpty
        ? state.nestedComments.first.comment
        : null;
  }

  Widget _buildReplyActions(
    BuildContext context,
    bool hasReplies,
    bool isExpanded,
    bool isLoadingReplies,
  ) {
    if (!hasReplies) return const SizedBox.shrink();

    return Row(
      children: [
        // Load/Hide replies button
        GestureDetector(
          onTap:
              isLoadingReplies
                  ? null
                  : () {
                    if (isExpanded) {
                      context.read<ReplyCommentCubit>().toggleExpanded(
                        nestedComment.comment.id,
                      );
                    } else {
                      // Load replies if not loaded yet
                      if (!nestedComment.hasLoadedReplies) {
                        context.read<ReplyCommentCubit>().loadReplies(
                          nestedComment.comment.id,
                        );
                      } else {
                        context.read<ReplyCommentCubit>().toggleExpanded(
                          nestedComment.comment.id,
                        );
                      }
                    }
                  },
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (isLoadingReplies)
                SizedBox(
                  width: 12,
                  height: 12,
                  child: CircularProgressIndicator(
                    strokeWidth: 1.5,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      themeData.neutral400,
                    ),
                  ),
                )
              else
                Icon(
                  isExpanded ? Icons.expand_less : Icons.expand_more,
                  size: 16,
                  color: themeData.neutral400,
                ),
              const SizedBox(width: 4),
              Text(
                isLoadingReplies
                    ? 'Loading...'
                    : isExpanded
                    ? 'Hide replies'
                    : 'View ${nestedComment.comment.replies} ${nestedComment.comment.replies == 1 ? 'reply' : 'replies'}',
                style: labelSmall.copyColor(themeData.neutral400),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
