import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_social/cubit/post/reply_comment/reply_comment_cubit.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/comment/comment_model.dart';
import 'package:toii_social/widget/bottom_sheet/tt_bottom_sheet.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/images/avatar_widget.dart';
import 'package:toii_social/widget/text_field.dart/text_field.dart';

class ReplyCommentBottomSheet extends StatefulWidget {
  final CommentItemModel parentComment;
  final String? currentUserName;
  final String? currentUserAvatar;

  const ReplyCommentBottomSheet({
    super.key,
    required this.parentComment,
    this.currentUserName,
    this.currentUserAvatar,
  });

  /// Show the reply bottom sheet
  static Future<void> show({
    required BuildContext context,
    required CommentItemModel parentComment,
    String? currentUserName,
    String? currentUserAvatar,
  }) {
    // Get the cubit from the current context before showing the bottom sheet
    final replyCommentCubit = context.read<ReplyCommentCubit>();

    return showTtBottomSheet(
      context,
      isDismissible: true,
      child: BlocProvider<ReplyCommentCubit>.value(
        value: replyCommentCubit,
        child: ReplyCommentBottomSheet(
          parentComment: parentComment,
          currentUserName: currentUserName,
          currentUserAvatar: currentUserAvatar,
        ),
      ),
    );
  }

  @override
  State<ReplyCommentBottomSheet> createState() =>
      _ReplyCommentBottomSheetState();
}

class _ReplyCommentBottomSheetState extends State<ReplyCommentBottomSheet> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // Auto focus when bottom sheet opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });

    // Listen to text changes to update send button state
    _controller.addListener(() {
      setState(() {
        // This will trigger a rebuild to update the send button color
      });
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _handleSend() async {
    final content = _controller.text.trim();
    if (content.isEmpty || _isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await context.read<ReplyCommentCubit>().createReply(
        parentCommentId: widget.parentComment.id,
        content: content,
      );

      if (mounted) {
        Navigator.of(context).pop(); // Close bottom sheet
      }
    } catch (e) {
      // Error handling is done in the cubit
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _handleCancel() {
    Navigator.of(context).pop();
  }

  String _getCommentPreview() {
    const maxLength = 50;
    final content = widget.parentComment.content;
    if (content.length <= maxLength) {
      return content;
    }
    return '${content.substring(0, maxLength)}...';
  }

  @override
  Widget build(BuildContext context) {
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;

    return BlocListener<ReplyCommentCubit, ReplyCommentState>(
      listener: (context, state) {
        if (state.status == ReplyCommentStatus.success &&
            !state.isCreatingReply) {
          // Reply was created successfully
          if (mounted) {
            // Close the bottom sheet first
            Navigator.of(context).pop();
          }
        }

        if (state.status == ReplyCommentStatus.failure) {
          setState(() {
            _isLoading = false;
          });
        }
      },
      child: Container(
        padding: EdgeInsets.only(bottom: 16 + keyboardHeight),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Assets.icons.icReply.svg(
                  width: 20,
                  height: 20,
                  colorFilter: ColorFilter.mode(
                    themeData.primaryGreen500,
                    BlendMode.srcIn,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  'Reply to comment',
                  style: titleMedium.copyColor(themeData.neutral800),
                ),
                const Spacer(),
                GestureDetector(
                  onTap: _handleCancel,
                  child: Icon(
                    Icons.close,
                    size: 24,
                    color: themeData.neutral400,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Parent comment preview
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: themeData.neutral200,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: themeData.neutral300, width: 1),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AvatarWidget(
                    size: 32,
                    name:
                        widget.parentComment.user?.fullName ??
                        widget.parentComment.user?.username ??
                        "User",
                    imageUrl: widget.parentComment.user?.avatar ?? "",
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.parentComment.user?.fullName ??
                              widget.parentComment.user?.username ??
                              "Someone",
                          style: labelMedium.copyColor(themeData.neutral500),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _getCommentPreview(),
                          style: bodySmall.copyColor(themeData.neutral500),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Input section
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                // Current user avatar
                AvatarWidget(
                  size: 40,
                  name: widget.currentUserName ?? "You",
                  imageUrl: widget.currentUserAvatar ?? "",
                ),
                const SizedBox(width: 12),

                // Text input using shared component
                Expanded(
                  child: TTextField(
                    textController: _controller,
                    focusNode: _focusNode,
                    hintText: "Write a reply...",
                    autofocus: true,
                    onEditingComplete: _handleSend,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                ),
                const SizedBox(width: 12),

                // Send button - keeping custom circular design for better UX
                GestureDetector(
                  onTap: _isLoading ? null : _handleSend,
                  child: Container(
                    width: 44,
                    height: 44,
                    decoration: BoxDecoration(
                      color:
                          _controller.text.trim().isEmpty || _isLoading
                              ? themeData.neutral300
                              : themeData.primaryGreen500,
                      shape: BoxShape.circle,
                    ),
                    child:
                        _isLoading
                            ? Center(
                              child: SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    themeData.neutral100,
                                  ),
                                ),
                              ),
                            )
                            : Icon(
                              Icons.send,
                              size: 20,
                              color:
                                  _controller.text.trim().isEmpty
                                      ? themeData.neutral500
                                      : themeData.neutral100,
                            ),
                  ),
                ),
              ],
            ),

            // Add some bottom padding for better visual spacing
            const SizedBox(height: 8),
          ],
        ),
      ),
    );
  }
}
