import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/cubit/post/home/<USER>';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/home/<USER>/home_appbar_widget.dart';
import 'package:toii_social/screen/home/<USER>/home_item_nft_showcase_widget.dart';
import 'package:toii_social/screen/home/<USER>/home_item_post_widget.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/pull_refresh/app_refresh_view.dart';
import 'package:toii_social/widget/tabbar/custom_tab_bar.dart';

class HomeMainPage extends StatefulWidget {
  final ScrollController _scrollController;
  final Function(ScrollDirection)? onScrollDirectionChanged;

  const HomeMainPage({
    super.key,
    required ScrollController scrollController,
    this.onScrollDirectionChanged,
  }) : _scrollController = scrollController;

  @override
  State<HomeMainPage> createState() => _HomeMainPageState();
}

class _HomeMainPageState extends State<HomeMainPage>
    with AutomaticKeepAliveClientMixin, TickerProviderStateMixin {
  final _homeCubit = GetIt.instance<HomeCubit>();
  late TabController _tabController;
  late ScrollController _nestedScrollController;
  double _lastScrollPosition = 0;
  ScrollDirection _lastDirection = ScrollDirection.idle;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _nestedScrollController = ScrollController();

    // Add listener to the nested scroll controller
    _nestedScrollController.addListener(() {
      if (_nestedScrollController.hasClients) {
        final currentPosition = _nestedScrollController.position.pixels;

        // Determine scroll direction based on position change
        ScrollDirection scrollDirection = ScrollDirection.idle;
        if (currentPosition > _lastScrollPosition + 2) {
          // Very small threshold
          scrollDirection =
              ScrollDirection.forward; // Scrolling up (from bottom to top)
        } else if (currentPosition < _lastScrollPosition - 2) {
          scrollDirection =
              ScrollDirection.reverse; // Scrolling down (from top to bottom)
        }

        // Only notify if direction actually changed
        if (scrollDirection != _lastDirection) {
          _lastDirection = scrollDirection;
          widget.onScrollDirectionChanged?.call(scrollDirection);
        }

        // Update last position
        _lastScrollPosition = currentPosition;
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _nestedScrollController.dispose();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  Widget _buildCustomTabBar() {
    return CustomTabBar(
      controller: _tabController,
      tabs: const ['For You', 'Following'],
      selectedFontWeight: FontWeight.w700,
      unselectedFontWeight: FontWeight.w400,
      selectedColor: themeData.neutral800,
      unselectedColor: themeData.neutral400,
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return MultiBlocProvider(
      providers: [BlocProvider(create: (_) => _homeCubit..getUserFeed())],
      child: Scaffold(
        backgroundColor: Theme.of(context).colorScheme.surface,
        body: BlocBuilder<HomeCubit, HomeState>(
          builder: (context, state) {
            return PullToRefreshView(
              onRefresh: () async {
                context.read<HomeCubit>().getUserFeed();
              },
              child: NestedScrollView(
                controller: _nestedScrollController,
                headerSliverBuilder: (context, innerBoxIsScrolled) {
                  return [
                    SliverLayoutBuilder(
                      builder: (BuildContext context, constraints) {
                        final scrolled = constraints.scrollOffset > 0;

                        return SliverAppBar(
                          backgroundColor:
                              scrolled
                                  ? Colors.transparent
                                  : Colors.transparent,
                          shadowColor: Colors.transparent,
                          pinned: true,
                          snap: false,
                          automaticallyImplyLeading: false,
                          floating: true,
                          expandedHeight: 84,
                          centerTitle: false,
                          flexibleSpace:
                              scrolled
                                  ? ClipRect(
                                    child: BackdropFilter(
                                      filter: ImageFilter.blur(
                                        sigmaX: 100,
                                        sigmaY: 100,
                                      ),
                                      child: spaceBar(scrolled),
                                    ),
                                  )
                                  : spaceBar(scrolled),
                          actions: [
                            Assets.icons.icNotification.svg(),
                            const SizedBox(width: 8),
                            GestureDetector(
                              onTap: () {
                                context.push(RouterEnums.ai.routeName);
                              },
                              child: SvgPicture.asset(
                                Assets.icons.icHomeAi.path,
                              ),
                            ),
                            const SizedBox(width: 16),
                          ],
                        );
                      },
                    ),
                    SliverToBoxAdapter(child: _buildCustomTabBar()),
                  ];
                },
                body: TabBarView(
                  controller: _tabController,
                  children: [
                    // For You Tab
                    CustomScrollView(
                      slivers: [
                        const SliverToBoxAdapter(
                          child: HomeItemNftShowcaseWidget(),
                        ),
                        SliverList(
                          delegate: SliverChildBuilderDelegate(
                            (context, index) => HomeItemPostWidget(
                              post: state.posts[index],
                              isShowActionMore: true,
                              isNotOnTap: false,
                            ),
                            childCount: state.posts.length,
                          ),
                        ),
                      ],
                    ),
                    // Following Tab
                    CustomScrollView(
                      slivers: [
                        const SliverToBoxAdapter(
                          child: HomeItemNftShowcaseWidget(),
                        ),
                        SliverList(
                          delegate: SliverChildBuilderDelegate(
                            (context, index) => HomeItemPostWidget(
                              post: state.posts[index],
                              isShowActionMore: true,
                              isNotOnTap: false,
                            ),
                            childCount: state.posts.length,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
