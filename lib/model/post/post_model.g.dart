// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'post_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PostResponseDataModel _$PostResponseDataModelFromJson(
        Map<String, dynamic> json) =>
    PostResponseDataModel(
      posts: (json['posts'] as List<dynamic>)
          .map((e) => PostModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      total: (json['total'] as num).toInt(),
    );

Map<String, dynamic> _$PostResponseDataModelToJson(
        PostResponseDataModel instance) =>
    <String, dynamic>{
      'posts': instance.posts,
      'total': instance.total,
    };

PostModel _$PostModelFromJson(Map<String, dynamic> json) => PostModel(
      id: json['id'] as String,
      user: json['user'] == null
          ? null
          : PostUserModel.fromJson(json['user'] as Map<String, dynamic>),
      content: json['content'] as String? ?? '',
      mediaKeys: (json['media_keys'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      mediaUrls: (json['media_urls'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      reposts: (json['reposts'] as num?)?.toInt() ?? 0,
      comments: (json['comments'] as num?)?.toInt() ?? 0,
      reactions: json['reactions'] == null
          ? null
          : ReactionGroupModel.fromJson(
              json['reactions'] as Map<String, dynamic>),
      privacy: json['privacy'] as String?,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
    );

Map<String, dynamic> _$PostModelToJson(PostModel instance) => <String, dynamic>{
      'id': instance.id,
      'user': instance.user,
      'content': instance.content,
      'media_keys': instance.mediaKeys,
      'media_urls': instance.mediaUrls,
      'reposts': instance.reposts,
      'comments': instance.comments,
      'reactions': instance.reactions,
      'privacy': instance.privacy,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };

PostUserModel _$PostUserModelFromJson(Map<String, dynamic> json) =>
    PostUserModel(
      id: json['id'] as String,
      username: json['username'] as String?,
      phoneNumber: json['phoneNumber'] as String?,
      email: json['email'] as String?,
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      fullName: json['full_name'] as String?,
      avatar: json['avatar'] as String?,
      address: json['address'] as String?,
      moreInfo: json['more_info'] as Map<String, dynamic>? ?? const {},
      lastActivity: json['last_activity'] as String?,
      role: json['role'] as String?,
      status: json['status'] as String?,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
      deletedAt: json['deleted_at'] as String?,
    );

Map<String, dynamic> _$PostUserModelToJson(PostUserModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'username': instance.username,
      'phoneNumber': instance.phoneNumber,
      'email': instance.email,
      'first_name': instance.firstName,
      'last_name': instance.lastName,
      'full_name': instance.fullName,
      'avatar': instance.avatar,
      'address': instance.address,
      'more_info': instance.moreInfo,
      'last_activity': instance.lastActivity,
      'role': instance.role,
      'status': instance.status,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'deleted_at': instance.deletedAt,
    };

CreatePostRequestModel _$CreatePostRequestModelFromJson(
        Map<String, dynamic> json) =>
    CreatePostRequestModel(
      content: json['content'] as String?,
      privacy: json['privacy'] as String,
      mediaKeys: (json['media_keys'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$CreatePostRequestModelToJson(
        CreatePostRequestModel instance) =>
    <String, dynamic>{
      'content': instance.content,
      'media_keys': instance.mediaKeys,
      'privacy': instance.privacy,
    };
